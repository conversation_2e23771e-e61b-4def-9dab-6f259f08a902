# Knaben API Search Tool

一个使用 Knaben API (https://knaben.org/api/v1/) 搜索种子的命令行工具。

## 功能特性

- 通过关键词搜索种子
- 获取最新种子（无需搜索）
- 按类别、时间、安全性过滤
- 按做种数、下载数、日期等排序
- 格式化显示搜索结果
- 支持磁力链接和下载链接
- 配置文件和预设支持
- 多种使用方式（Python 脚本、批处理文件、PowerShell 脚本）

## 安装要求

确保你已安装 Python 3.6+ 和 `requests` 库：

```bash
pip install requests
```

## 使用方法

### 方式一：直接运行批处理文件（推荐）

```bash
# 双击 search.bat 文件，然后输入搜索关键词
search.bat

# 或者直接传递参数
search.bat "Ubuntu Linux"
search.bat "Avengers" --size 10
```

### 方式二：使用 PowerShell 脚本

```powershell
# 运行 PowerShell 脚本
.\search.ps1 "Ubuntu Linux"
```

### 方式三：直接使用 Python 脚本

```bash
# 基础搜索
python knaben_search.py "Ubuntu Linux"
python knaben_search.py "Avengers Endgame"
python knaben_search.py "Pink Floyd"
```

### 方式四：使用增强版搜索（支持预设）

```bash
# 使用预设搜索电影
python enhanced_search.py "Avengers" --preset movies

# 使用预设搜索音乐
python enhanced_search.py "Pink Floyd" --preset music

# 查看可用预设
python enhanced_search.py --list-presets
```

### Advanced Options

```bash
# Get recent torrents without search
python knaben_search.py --recent

# Limit number of results
python knaben_search.py "Ubuntu" --size 10

# Order by different fields
python knaben_search.py "Ubuntu" --order-by peers --order-direction asc

# Search in specific field
python knaben_search.py "Ubuntu" --search-field title

# Filter by categories (see Knaben website for category IDs)
python knaben_search.py "Ubuntu" --categories 5001000 5004000

# Show results from last 24 hours only
python knaben_search.py "Ubuntu" --hours 24

# Include potentially unsafe results
python knaben_search.py "Ubuntu" --show-unsafe

# Include adult content
python knaben_search.py "Ubuntu" --show-xxx
```

### Help

```bash
python knaben_search.py --help
```

## API Parameters

The tool supports all Knaben API v1 parameters:

- **query**: Search query string
- **search_type**: "score" or percentage value (default: "score")
- **search_field**: Field to search in (e.g., "title")
- **order_by**: Field to order by (default: "seeders")
- **order_direction**: "desc" or "asc" (default: "desc")
- **categories**: List of category IDs
- **from**: Pagination offset (default: 0)
- **size**: Number of results (default: 20, max: 300)
- **hide_unsafe**: Filter out potentially unsafe results (default: true)
- **hide_xxx**: Hide adult content (default: true)
- **seconds_since_last_seen**: Filter by last seen time

## Example Output

```
Searching for: Ubuntu Linux

Found 1247 results:
================================================================================
 1. Ubuntu 22.04.3 LTS Desktop amd64
    Size: 4.7 GB | S: 156 | P: 23 | Date: 2023-08-10
    Tracker: The Pirate Bay | Category: Applications / Other
    Magnet: magnet:?xt=urn:btih:...

 2. Ubuntu Server 22.04.3 LTS amd64
    Size: 1.4 GB | S: 89 | P: 12 | Date: 2023-08-10
    Tracker: 1337x | Category: Applications / Other
    Link: https://knaben.eu/live/dl/1337x/?path=...
```

## Category IDs

Common category IDs (check Knaben website for complete list):

- **1000000**: Audio
- **1001000**: Audio / MP3
- **2000000**: Video
- **2001000**: Video / Movies
- **2002000**: Video / TV shows
- **5000000**: Applications
- **5001000**: Applications / Windows
- **5004000**: Applications / Linux

## Error Handling

The tool includes error handling for:

- Network timeouts
- Invalid API responses
- JSON parsing errors
- Missing parameters

## API Rate Limiting

Please be respectful of the Knaben API and avoid making too many requests in a short time period.
