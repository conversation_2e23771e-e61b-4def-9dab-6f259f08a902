#!/usr/bin/env python3
"""
Example usage of the Knaben API client
"""

from knaben_search import KnabenAPI, display_results


def main():
    # Create API client
    api = KnabenAPI()
    
    print("=== Knaben API Example Usage ===\n")
    
    # Example 1: Basic search
    print("1. Basic search for 'Ubuntu Linux':")
    results = api.search(
        query="Ubuntu Linux",
        size=5  # Limit to 5 results for demo
    )
    if results:
        display_results(results)
    
    print("\n" + "="*80 + "\n")
    
    # Example 2: Search with specific parameters
    print("2. Advanced search for movies, ordered by peers:")
    results = api.search(
        query="Avengers",
        search_field="title",
        order_by="peers",
        order_direction="desc",
        categories=[2001000],  # Video / Movies category
        size=3,
        hide_xxx=True,
        hide_unsafe=True
    )
    if results:
        display_results(results)
    
    print("\n" + "="*80 + "\n")
    
    # Example 3: Get recent torrents
    print("3. Recent torrents (no search query):")
    results = api.get_recent(size=5)
    if results:
        display_results(results)
    
    print("\n" + "="*80 + "\n")
    
    # Example 4: Search with time filter
    print("4. Search for 'Linux' from last 24 hours:")
    results = api.search(
        query="Linux",
        seconds_since_last_seen=86400,  # 24 hours in seconds
        size=3
    )
    if results:
        display_results(results)
    else:
        print("No recent results found")


if __name__ == "__main__":
    main()
