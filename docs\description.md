我打算用 python 建造一个自动化的影视库后端
关于几个部分：

1. 影视库 api -也就是前端会 call 这些 api 来进行影视库的交互
2. 影视库的视频来源 - 和 pikpak 的 api 进行交互
3. 刮削 -获取影视库的元数据
4. 磁力链接转换 - 用自动化工具获取影视库的磁力链接 ✅ **已完成基础模块**
5. 基本的用户管理功能

## 已完成模块

### 磁力链接转换模块 (magnet-xiongmao) ✅

- 📍 位置: `magnet-xiongmao/`
- 🎯 功能: 专门用于 xiongmaobt.org 的独立爬虫模块
- 📊 特性:
  - 🤖 智能浏览器控制 (基于 DrissionPage)
  - 📊 全面数据捕获 (HTML、截图、交互日志)
  - 🛡️ 多层反检测机制
  - 🔄 自动页面变化监控
  - 🎮 交互式和自动化双模式
  - 📝 结构化日志记录
  - ⚙️ 灵活配置系统

### 模块结构

```
magnet-xiongmao/
├── __init__.py          # 模块导出和快速启动函数
├── scraper.py           # 核心爬虫类 XiongmaoBTScraper
├── config.py            # 配置管理 Config
├── utils.py             # 工具类 (CaptureManager, SessionLogger, etc.)
├── main.py              # 主启动程序 (支持命令行参数)
├── run.py               # 简单运行器 (交互式菜单)
├── launcher.py          # 启动器 (选择运行方式)
├── examples.py          # 使用示例
└── README.md            # 详细文档
```

### 使用方法

#### 快速启动

```bash
# 安装依赖
pip install DrissionPage

# 进入模块目录
cd magnet-xiongmao

# 方式1: 启动器 (选择运行方式)
python launcher.py

# 方式2: 直接运行
python run.py                     # 简单运行 (推荐新手)
python main.py                    # 主程序 (支持命令行参数)
python main.py --monitor          # 自动监控模式
python main.py --headless         # 无头模式

# 方式3: 作为模块运行
python -m magnet_xiongmao.main --help
```

#### 作为模块调用

```python
# 从项目根目录导入
from magnet_xiongmao import XiongmaoBTScraper, Config, quick_start

# 快速开始
scraper = quick_start()
scraper.setup_browser()
scraper.navigate_to_target()
scraper.start_interactive_session()
scraper.close()

# 使用上下文管理器
with XiongmaoBTScraper() as scraper:
    scraper.navigate_to_target()
    scraper.start_interactive_session()

# 自定义配置
config = Config(capture_dir="my_data", headless=True)
scraper = XiongmaoBTScraper(config=config)
```

#### 运行示例

```python
# 在模块内运行示例
cd magnet-xiongmao
python examples.py

# 或在代码中调用
from magnet_xiongmao.examples import run_examples
run_examples()
```

### 捕获数据结构

```
captures/xiongmaobt/
├── html/                    # HTML 快照
├── screenshots/             # 页面截图
├── interactions/            # 详细交互信息
├── logs/                    # 日志文件
└── session_log_*.json       # 完整会话日志
```
