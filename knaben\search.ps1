# Knaben Search Tool PowerShell Script
# Usage: .\search.ps1 "search query" [additional options]

param(
    [string]$Query,
    [string[]]$AdditionalArgs
)

# Change to script directory
Set-Location $PSScriptRoot

if (-not $Query) {
    Write-Host "Knaben Torrent Search Tool" -ForegroundColor Green
    Write-Host ""
    $Query = Read-Host "Enter search query"
    
    if (-not $Query) {
        Write-Host "No query entered. Exiting..." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    # Default search with 10 results
    python knaben_search.py $Query --size 10
} else {
    # Pass all arguments to the Python script
    if ($AdditionalArgs) {
        python knaben_search.py $Query $AdditionalArgs
    } else {
        python knaben_search.py $Query
    }
}

Read-Host "Press Enter to exit"
