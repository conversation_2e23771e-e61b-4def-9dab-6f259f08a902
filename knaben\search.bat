@echo off
REM Knaben Search Tool Batch Script
REM Usage: search.bat "search query" [additional options]

cd /d "%~dp0"

if "%1"=="" (
    echo Knaben Torrent Search Tool
    echo.
    set /p query="Enter search query: "
    if "!query!"=="" (
        echo No query entered. Exiting...
        pause
        exit /b 1
    )
    python knaben_search.py "!query!" --size 10
) else (
    python knaben_search.py %*
)
pause
