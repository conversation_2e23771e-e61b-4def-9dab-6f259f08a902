#!/usr/bin/env python3
"""
Direct API test to check if adult content is available
"""

import requests
import json

def test_api():
    url = 'https://api.knaben.org/v1'
    
    # Test with explicit adult content search
    payload = {
        'query': 'jav',
        'hide_xxx': False,
        'hide_unsafe': False,
        'size': 5
    }
    
    print("Testing API with payload:")
    print(json.dumps(payload, indent=2))
    print()
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        print(f"Total results: {data.get('total', {}).get('value', 0)}")
        print("=" * 50)
        
        for i, hit in enumerate(data.get('hits', [])[:5], 1):
            title = hit.get('title', 'Unknown')
            category = hit.get('category', 'Unknown')
            category_ids = hit.get('categoryId', [])
            tracker = hit.get('tracker', 'Unknown')
            
            print(f"{i}. {title}")
            print(f"   Category: {category}")
            print(f"   Category IDs: {category_ids}")
            print(f"   Tracker: {tracker}")
            print()
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_api()
