#!/usr/bin/env python3
"""
Enhanced Knaben API Search Tool with configuration support
"""

import json
import os
import argparse
import sys
from knaben_search import KnabenAPI, display_results


def load_config():
    """Load configuration from config.json"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Warning: config.json not found, using default settings")
        return {
            "default_settings": {
                "size": 20,
                "order_by": "seeders",
                "order_direction": "desc",
                "hide_unsafe": True,
                "hide_xxx": True
            },
            "categories": {},
            "presets": {}
        }
    except json.JSONDecodeError as e:
        print(f"Error parsing config.json: {e}")
        sys.exit(1)


def get_category_ids(config, category_names):
    """Convert category names to IDs"""
    if not category_names:
        return None
    
    categories = config.get('categories', {})
    ids = []
    
    for name in category_names:
        if name.lower() in categories:
            ids.extend(categories[name.lower()])
        else:
            try:
                # Try to parse as integer ID
                ids.append(int(name))
            except ValueError:
                print(f"Warning: Unknown category '{name}', ignoring")
    
    return ids if ids else None


def apply_preset(config, preset_name):
    """Apply a preset configuration"""
    presets = config.get('presets', {})
    if preset_name not in presets:
        print(f"Error: Preset '{preset_name}' not found")
        print(f"Available presets: {', '.join(presets.keys())}")
        sys.exit(1)
    
    return presets[preset_name]


def main():
    config = load_config()
    default_settings = config.get('default_settings', {})
    
    parser = argparse.ArgumentParser(description='Enhanced Knaben API Search Tool')
    parser.add_argument('query', nargs='?', help='Search query')
    parser.add_argument('--recent', action='store_true', help='Get recent torrents without search')
    parser.add_argument('--preset', help='Use a preset configuration (movies, tv_shows, music, software, recent)')
    parser.add_argument('--size', type=int, default=default_settings.get('size', 20), 
                       help=f'Number of results (default: {default_settings.get("size", 20)})')
    parser.add_argument('--order-by', default=default_settings.get('order_by', 'seeders'), 
                       help=f'Order by field (default: {default_settings.get("order_by", "seeders")})')
    parser.add_argument('--order-direction', choices=['desc', 'asc'], 
                       default=default_settings.get('order_direction', 'desc'),
                       help='Order direction')
    parser.add_argument('--search-field', help='Search in specific field (e.g., title)')
    parser.add_argument('--categories', nargs='+', help='Category names or IDs to filter by')
    parser.add_argument('--show-unsafe', action='store_true', 
                       help='Show potentially unsafe results')
    parser.add_argument('--show-xxx', action='store_true', 
                       help='Show adult content')
    parser.add_argument('--hours', type=int, help='Only show results seen in last N hours')
    parser.add_argument('--list-categories', action='store_true', 
                       help='List available category names')
    parser.add_argument('--list-presets', action='store_true', 
                       help='List available presets')
    
    args = parser.parse_args()
    
    # Handle list commands
    if args.list_categories:
        categories = config.get('categories', {})
        print("Available categories:")
        for name, ids in categories.items():
            print(f"  {name}: {ids}")
        return
    
    if args.list_presets:
        presets = config.get('presets', {})
        print("Available presets:")
        for name, settings in presets.items():
            print(f"  {name}: {settings}")
        return
    
    # Apply preset if specified
    preset_settings = {}
    if args.preset:
        preset_settings = apply_preset(config, args.preset)
        print(f"Using preset: {args.preset}")
    
    # Merge settings: defaults < preset < command line args
    final_settings = {}
    final_settings.update(default_settings)
    final_settings.update(preset_settings)
    
    # Override with command line arguments
    if args.size != default_settings.get('size', 20):
        final_settings['size'] = args.size
    if args.order_by != default_settings.get('order_by', 'seeders'):
        final_settings['order_by'] = args.order_by
    if args.order_direction != default_settings.get('order_direction', 'desc'):
        final_settings['order_direction'] = args.order_direction

    # Handle show flags - only override if explicitly set
    if args.show_unsafe:
        final_settings['hide_unsafe'] = False
    elif 'hide_unsafe' not in final_settings:
        final_settings['hide_unsafe'] = default_settings.get('hide_unsafe', True)

    if args.show_xxx:
        final_settings['hide_xxx'] = False
    elif 'hide_xxx' not in final_settings:
        final_settings['hide_xxx'] = default_settings.get('hide_xxx', True)
    
    # Handle categories
    categories = None
    if args.categories:
        categories = get_category_ids(config, args.categories)
    elif 'categories' in preset_settings:
        categories = preset_settings['categories']
    
    # Validate input
    if not args.query and not args.recent:
        print("Please provide a search query or use --recent flag")
        parser.print_help()
        sys.exit(1)
    
    api = KnabenAPI()
    
    if args.recent:
        print("Fetching recent torrents...")
        results = api.get_recent(final_settings.get('size', 20))
    else:
        print(f"Searching for: {args.query}")
        if args.preset:
            print(f"Using preset: {args.preset}")
        
        # Convert hours to seconds if specified
        seconds_since_last_seen = None
        if args.hours:
            seconds_since_last_seen = args.hours * 3600
        
        results = api.search(
            query=args.query,
            search_field=args.search_field,
            order_by=final_settings.get('order_by', 'seeders'),
            order_direction=final_settings.get('order_direction', 'desc'),
            categories=categories,
            size=final_settings.get('size', 20),
            hide_unsafe=final_settings.get('hide_unsafe', True),
            hide_xxx=final_settings.get('hide_xxx', True),
            seconds_since_last_seen=seconds_since_last_seen
        )
    
    if results:
        display_results(results)
    else:
        print("Failed to get results from API")
        sys.exit(1)


if __name__ == "__main__":
    main()
